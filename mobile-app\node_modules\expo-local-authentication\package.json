{"name": "expo-local-authentication", "version": "13.4.1", "description": "Provides an API for FaceID and TouchID (iOS) or the Fingerprint API (Android) to authenticate the user with a face or fingerprint scan.", "main": "build/LocalAuthentication.js", "types": "build/LocalAuthentication.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "authentication", "auth", "touchID", "faceID", "fingerprint"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-local-authentication"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/local-authentication/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"invariant": "^2.2.4"}, "devDependencies": {"expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "3ccd2edee9cbfed217557675cb50f0ba5e55a9e4"}