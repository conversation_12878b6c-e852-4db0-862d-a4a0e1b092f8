import React, { useState, useRef } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { theme } from '../theme/theme';

export const ModernInput = ({
  label,
  value,
  onChangeText,
  placeholder,
  error,
  helperText,
  leftIcon,
  rightIcon,
  onRightIconPress,
  secureTextEntry = false,
  multiline = false,
  numberOfLines = 1,
  variant = 'outlined', // outlined, filled
  disabled = false,
  style,
  inputStyle,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);
  const animatedValue = useRef(new Animated.Value(value ? 1 : 0)).current;

  const handleFocus = () => {
    setIsFocused(true);
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: theme.animations.timing.fast,
      useNativeDriver: false,
    }).start();
  };

  const handleBlur = () => {
    setIsFocused(false);
    if (!value) {
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: theme.animations.timing.fast,
        useNativeDriver: false,
      }).start();
    }
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const getContainerStyle = () => {
    const baseStyle = {
      borderRadius: theme.borderRadius.md,
      minHeight: multiline ? 80 : 56,
    };

    if (variant === 'filled') {
      return {
        ...baseStyle,
        backgroundColor: theme.colors.surfaceVariant,
        borderBottomWidth: 2,
        borderBottomColor: error 
          ? theme.colors.error 
          : isFocused 
            ? theme.colors.primary 
            : theme.colors.outline,
      };
    }

    // outlined variant
    return {
      ...baseStyle,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: error 
        ? theme.colors.error 
        : isFocused 
          ? theme.colors.primary 
          : theme.colors.outline,
    };
  };

  const getLabelStyle = () => {
    const fontSize = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [16, 12],
    });

    const translateY = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, -24],
    });

    return {
      position: 'absolute',
      left: leftIcon ? 48 : 16,
      fontSize,
      transform: [{ translateY }],
      color: error 
        ? theme.colors.error 
        : isFocused 
          ? theme.colors.primary 
          : theme.colors.onSurfaceVariant,
      backgroundColor: variant === 'outlined' ? theme.colors.surface : 'transparent',
      paddingHorizontal: variant === 'outlined' ? 4 : 0,
      zIndex: 1,
    };
  };

  return (
    <View style={[styles.container, style]}>
      <View style={[styles.inputContainer, getContainerStyle()]}>
        {leftIcon && (
          <View style={styles.leftIconContainer}>
            <Ionicons
              name={leftIcon}
              size={20}
              color={isFocused ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
          </View>
        )}

        {label && (
          <Animated.Text style={getLabelStyle()}>
            {label}
          </Animated.Text>
        )}

        <TextInput
          style={[
            styles.input,
            {
              paddingLeft: leftIcon ? 48 : 16,
              paddingRight: (rightIcon || secureTextEntry) ? 48 : 16,
              paddingTop: label ? 24 : 16,
              textAlignVertical: multiline ? 'top' : 'center',
            },
            inputStyle,
          ]}
          value={value}
          onChangeText={onChangeText}
          placeholder={!label ? placeholder : ''}
          placeholderTextColor={theme.colors.onSurfaceVariant}
          onFocus={handleFocus}
          onBlur={handleBlur}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          multiline={multiline}
          numberOfLines={numberOfLines}
          editable={!disabled}
          {...props}
        />

        {(rightIcon || secureTextEntry) && (
          <TouchableOpacity
            style={styles.rightIconContainer}
            onPress={secureTextEntry ? togglePasswordVisibility : onRightIconPress}
            disabled={!secureTextEntry && !onRightIconPress}
          >
            <Ionicons
              name={
                secureTextEntry
                  ? isPasswordVisible
                    ? 'eye-off-outline'
                    : 'eye-outline'
                  : rightIcon
              }
              size={20}
              color={isFocused ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
          </TouchableOpacity>
        )}
      </View>

      {(error || helperText) && (
        <Text style={[
          styles.helperText,
          { color: error ? theme.colors.error : theme.colors.onSurfaceVariant }
        ]}>
          {error || helperText}
        </Text>
      )}
    </View>
  );
};

// Specialized input variants
export const SearchInput = ({ onSearch, ...props }) => (
  <ModernInput
    leftIcon="search-outline"
    placeholder="Search..."
    variant="filled"
    onChangeText={onSearch}
    {...props}
  />
);

export const PasswordInput = ({ ...props }) => (
  <ModernInput
    secureTextEntry={true}
    leftIcon="lock-closed-outline"
    {...props}
  />
);

export const EmailInput = ({ ...props }) => (
  <ModernInput
    leftIcon="mail-outline"
    keyboardType="email-address"
    autoCapitalize="none"
    {...props}
  />
);

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },
  inputContainer: {
    position: 'relative',
    justifyContent: 'center',
  },
  input: {
    ...theme.typography.bodyLarge,
    color: theme.colors.onSurface,
    paddingVertical: 16,
    margin: 0,
  },
  leftIconContainer: {
    position: 'absolute',
    left: 16,
    zIndex: 2,
  },
  rightIconContainer: {
    position: 'absolute',
    right: 16,
    zIndex: 2,
  },
  helperText: {
    ...theme.typography.bodySmall,
    marginTop: theme.spacing.xs,
    marginLeft: theme.spacing.md,
  },
});

export default ModernInput;
