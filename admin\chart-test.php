<?php
session_start();

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js Test - Admin Panel</title>
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>Chart.js Test Page
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Chart.js Status -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Chart.js Status</h6>
                                    <p id="chartStatus" class="mb-0">Checking Chart.js availability...</p>
                                </div>
                            </div>
                        </div>

                        <!-- Test Charts -->
                        <div class="row">
                            <!-- Doughnut Chart Test -->
                            <div class="col-md-6 mb-4">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Doughnut Chart Test</h6>
                                    </div>
                                    <div class="card-body text-center">
                                        <div style="width: 300px; height: 300px; margin: 0 auto;">
                                            <canvas id="testDoughnut"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Line Chart Test -->
                            <div class="col-md-6 mb-4">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Line Chart Test</h6>
                                    </div>
                                    <div class="card-body">
                                        <div style="height: 300px;">
                                            <canvas id="testLine"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Test Results -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Test Results</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="testResults">
                                            <p class="text-muted">Test results will appear here...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <button class="btn btn-primary me-2" onclick="runTests()">
                                    <i class="fas fa-play me-2"></i>Run Tests
                                </button>
                                <a href="dashboard.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js (same as admin footer) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js" integrity="sha512-ElRFoEQdI5Ht6kZvyzXhYG9NqjtkmlkfYk0wr6wHxU9JEHakS7UJZNeml5ALk+8IKlU6jDgMabC3vkumRokgJA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            checkChartJS();
            runTests();
        });

        function checkChartJS() {
            const statusEl = document.getElementById('chartStatus');
            
            if (typeof Chart !== 'undefined') {
                statusEl.innerHTML = `
                    <span class="text-success">
                        <i class="fas fa-check-circle me-2"></i>Chart.js is loaded successfully!
                        <br><small>Version: ${Chart.version || 'Unknown'}</small>
                    </span>
                `;
            } else {
                statusEl.innerHTML = `
                    <span class="text-danger">
                        <i class="fas fa-times-circle me-2"></i>Chart.js is NOT loaded!
                    </span>
                `;
            }
        }

        function runTests() {
            const resultsEl = document.getElementById('testResults');
            let results = [];

            // Test 1: Chart.js availability
            if (typeof Chart !== 'undefined') {
                results.push('<div class="alert alert-success"><i class="fas fa-check me-2"></i>Chart.js library is available</div>');
                
                try {
                    // Test 2: Create Doughnut Chart
                    const doughnutCtx = document.getElementById('testDoughnut');
                    if (doughnutCtx) {
                        new Chart(doughnutCtx, {
                            type: 'doughnut',
                            data: {
                                labels: ['Completed', 'Ongoing', 'Planned'],
                                datasets: [{
                                    data: [12, 8, 5],
                                    backgroundColor: [
                                        'rgba(25, 135, 84, 0.8)',
                                        'rgba(255, 193, 7, 0.8)',
                                        'rgba(13, 202, 240, 0.8)'
                                    ]
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: true,
                                plugins: {
                                    legend: {
                                        position: 'bottom'
                                    }
                                }
                            }
                        });
                        results.push('<div class="alert alert-success"><i class="fas fa-check me-2"></i>Doughnut chart created successfully</div>');
                    }

                    // Test 3: Create Line Chart
                    const lineCtx = document.getElementById('testLine');
                    if (lineCtx) {
                        new Chart(lineCtx, {
                            type: 'line',
                            data: {
                                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                                datasets: [{
                                    label: 'Projects',
                                    data: [12, 19, 3, 5, 2, 3],
                                    borderColor: 'rgba(13, 110, 253, 1)',
                                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                                    fill: true
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false
                            }
                        });
                        results.push('<div class="alert alert-success"><i class="fas fa-check me-2"></i>Line chart created successfully</div>');
                    }

                } catch (error) {
                    results.push(`<div class="alert alert-danger"><i class="fas fa-times me-2"></i>Error creating charts: ${error.message}</div>`);
                }
            } else {
                results.push('<div class="alert alert-danger"><i class="fas fa-times me-2"></i>Chart.js library is not available</div>');
            }

            resultsEl.innerHTML = results.join('');
        }
    </script>
</body>
</html>
