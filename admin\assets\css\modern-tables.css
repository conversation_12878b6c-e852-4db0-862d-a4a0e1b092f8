/* Modern Table Styling for Admin Panel */

/* Enhanced Table Hover Effects */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.03) !important;
    transform: translateY(-1px);
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Modern Card Styling */
.card.shadow-sm {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    border-radius: 12px !important;
    overflow: hidden;
}

.card-header.bg-white {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Enhanced Table Headers */
.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border: none !important;
    font-weight: 600 !important;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-size: 0.75rem !important;
    color: #6c757d !important;
    padding: 1rem !important;
}

/* Modern Badge Styling */
.badge.rounded-pill {
    font-weight: 500 !important;
    letter-spacing: 0.25px;
    padding: 0.5rem 1rem !important;
    font-size: 0.75rem !important;
}

/* Enhanced Button Groups */
.btn-group .btn {
    border-radius: 8px !important;
    margin: 0 2px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.btn-group .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Modern Input Styling */
.input-group .form-control.bg-light {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 0 8px 8px 0 !important;
    transition: all 0.2s ease;
}

.input-group .form-control.bg-light:focus {
    background-color: #ffffff !important;
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.1) !important;
}

.input-group-text.bg-light {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 8px 0 0 8px !important;
    border-right: none !important;
}

/* Enhanced Row Styling */
.table tbody tr {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.table tbody tr:last-child {
    border-bottom: none !important;
}

/* Status Indicators */
.badge.bg-success.bg-opacity-15 {
    background-color: rgba(25, 135, 84, 0.1) !important;
    color: #198754 !important;
    border: 1px solid rgba(25, 135, 84, 0.2) !important;
}

.badge.bg-warning.bg-opacity-15 {
    background-color: rgba(255, 193, 7, 0.1) !important;
    color: #ffc107 !important;
    border: 1px solid rgba(255, 193, 7, 0.2) !important;
}

.badge.bg-info.bg-opacity-15 {
    background-color: rgba(13, 202, 240, 0.1) !important;
    color: #0dcaf0 !important;
    border: 1px solid rgba(13, 202, 240, 0.2) !important;
}

.badge.bg-danger.bg-opacity-15 {
    background-color: rgba(220, 53, 69, 0.1) !important;
    color: #dc3545 !important;
    border: 1px solid rgba(220, 53, 69, 0.2) !important;
}

.badge.bg-primary.bg-opacity-15 {
    background-color: rgba(13, 110, 253, 0.1) !important;
    color: #0d6efd !important;
    border: 1px solid rgba(13, 110, 253, 0.2) !important;
}

.badge.bg-secondary.bg-opacity-15 {
    background-color: rgba(108, 117, 125, 0.1) !important;
    color: #6c757d !important;
    border: 1px solid rgba(108, 117, 125, 0.2) !important;
}

/* Action Button Styling */
.btn.bg-primary.bg-opacity-10 {
    background-color: rgba(13, 110, 253, 0.1) !important;
    color: #0d6efd !important;
    border: 1px solid rgba(13, 110, 253, 0.2) !important;
}

.btn.bg-success.bg-opacity-10 {
    background-color: rgba(25, 135, 84, 0.1) !important;
    color: #198754 !important;
    border: 1px solid rgba(25, 135, 84, 0.2) !important;
}

.btn.bg-warning.bg-opacity-10 {
    background-color: rgba(255, 193, 7, 0.1) !important;
    color: #ffc107 !important;
    border: 1px solid rgba(255, 193, 7, 0.2) !important;
}

.btn.bg-info.bg-opacity-10 {
    background-color: rgba(13, 202, 240, 0.1) !important;
    color: #0dcaf0 !important;
    border: 1px solid rgba(13, 202, 240, 0.2) !important;
}

.btn.bg-danger.bg-opacity-10 {
    background-color: rgba(220, 53, 69, 0.1) !important;
    color: #dc3545 !important;
    border: 1px solid rgba(220, 53, 69, 0.2) !important;
}

/* Enhanced Empty State */
.text-center.py-5 {
    padding: 3rem 1.5rem !important;
}

.text-center.py-5 .btn {
    border-radius: 10px !important;
    padding: 0.75rem 2rem !important;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.text-center.py-5 .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Icon Containers */
.d-inline-flex.bg-light.rounded-circle {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.d-inline-flex.bg-primary.bg-opacity-10.rounded-circle {
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1) 0%, rgba(13, 110, 253, 0.05) 100%) !important;
    border: 1px solid rgba(13, 110, 253, 0.2);
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .table-responsive {
        border-radius: 8px;
        overflow-x: auto;
    }

    .btn-group .btn {
        margin: 1px;
        padding: 0.375rem 0.5rem;
    }

    .card-header .input-group {
        width: 100% !important;
        margin-top: 0.5rem;
    }

    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Tooltip Enhancements */
.tooltip {
    font-size: 0.75rem;
}

.tooltip-inner {
    background-color: #212529;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* Focus States */
.btn:focus,
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15) !important;
}

/* Scroll Indicators */
.scroll-indicator {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.scroll-indicator-left {
    left: 10px;
}

.scroll-indicator-right {
    right: 10px;
}

/* Search Empty State */
.search-empty-state td {
    background-color: #f8f9fa !important;
    border: none !important;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
}

/* Enhanced Focus States */
.table tbody tr:focus-within {
    background-color: rgba(13, 110, 253, 0.05) !important;
    outline: 2px solid rgba(13, 110, 253, 0.25);
    outline-offset: -2px;
}

/* Print Styles */
@media print {

    .btn-group,
    .input-group,
    .card-header {
        display: none !important;
    }

    .table {
        border-collapse: collapse !important;
    }

    .table th,
    .table td {
        border: 1px solid #dee2e6 !important;
    }

    .scroll-indicator {
        display: none !important;
    }
}