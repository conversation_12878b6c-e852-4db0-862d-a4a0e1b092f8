<?php
/**
 * Sidebar Navigation Component
 * This file contains the sidebar navigation for the admin panel
 * Note: The sidebar is now integrated into admin_header.php for better layout control
 * This file is kept for compatibility and can be used for additional sidebar functionality
 */

// Get current page for navigation highlighting
$current_page = basename($_SERVER['PHP_SELF']);

// Function to check if current page matches navigation item
function isActivePage($page) {
    global $current_page;
    return ($current_page == $page) ? 'active' : '';
}

// Function to get navigation badge count (implement as needed)
function getNavBadgeCount($type) {
    switch ($type) {
        case 'projects':
            // Implement project count logic
            return 0;
        case 'messages':
            // Implement unread message count logic
            return 0;
        case 'notifications':
            // Implement notification count logic
            return 0;
        default:
            return 0;
    }
}

// Navigation menu structure
$navigation_menu = [
    'main' => [
        'title' => 'Main',
        'items' => [
            [
                'url' => 'dashboard.php',
                'icon' => 'fas fa-tachometer-alt',
                'label' => 'Dashboard',
                'badge' => null
            ]
        ]
    ],
    'projects' => [
        'title' => 'Projects',
        'items' => [
            [
                'url' => 'projects.php',
                'icon' => 'fas fa-building',
                'label' => 'All Projects',
                'badge' => getNavBadgeCount('projects')
            ]
        ]
    ],
    'services' => [
        'title' => 'Services',
        'items' => [
            [
                'url' => 'services.php',
                'icon' => 'fas fa-tools',
                'label' => 'All Services',
                'badge' => null
            ]
        ]
    ],
    'content' => [
        'title' => 'Content',
        'items' => [
            [
                'url' => 'media.php',
                'icon' => 'fas fa-images',
                'label' => 'Media Library',
                'badge' => null
            ],
            [
                'url' => 'messages.php',
                'icon' => 'fas fa-envelope',
                'label' => 'Messages',
                'badge' => getNavBadgeCount('messages')
            ]
        ]
    ],
    'system' => [
        'title' => 'System',
        'items' => [
            [
                'url' => 'users.php',
                'icon' => 'fas fa-users',
                'label' => 'Users',
                'badge' => null
            ],
            [
                'url' => 'settings.php',
                'icon' => 'fas fa-cog',
                'label' => 'Settings',
                'badge' => null
            ],
            [
                'url' => 'profile.php',
                'icon' => 'fas fa-user',
                'label' => 'Profile',
                'badge' => null
            ]
        ]
    ]
];

/**
 * Render navigation section
 */
function renderNavSection($section_key, $section_data) {
    global $current_page;
    
    echo '<div class="nav-section">';
    echo '<div class="nav-section-title">' . htmlspecialchars($section_data['title']) . '</div>';
    
    foreach ($section_data['items'] as $item) {
        $active_class = ($current_page == $item['url']) ? 'active' : '';
        $badge_html = '';
        
        if ($item['badge'] && $item['badge'] > 0) {
            $badge_color = ($item['label'] == 'Messages') ? 'bg-danger' : 'bg-primary';
            $badge_html = '<span class="nav-badge badge ' . $badge_color . '">' . $item['badge'] . '</span>';
        }
        
        echo '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link ' . $active_class . '">';
        echo '<i class="' . htmlspecialchars($item['icon']) . '"></i>';
        echo htmlspecialchars($item['label']);
        echo $badge_html;
        echo '</a>';
    }
    
    echo '</div>';
}

/**
 * Render complete sidebar navigation
 */
function renderSidebarNavigation() {
    global $navigation_menu;
    
    echo '<div class="sidebar-nav">';
    
    foreach ($navigation_menu as $section_key => $section_data) {
        renderNavSection($section_key, $section_data);
    }
    
    echo '</div>';
}

/**
 * Get sidebar brand information
 */
function getSidebarBrand() {
    return [
        'icon' => 'fas fa-hammer',
        'title' => 'Flori Construction',
        'subtitle' => 'Admin Panel'
    ];
}

/**
 * Render sidebar brand
 */
function renderSidebarBrand() {
    $brand = getSidebarBrand();
    
    echo '<div class="sidebar-brand">';
    echo '<div class="brand-icon">';
    echo '<i class="' . htmlspecialchars($brand['icon']) . '"></i>';
    echo '</div>';
    echo '<div class="brand-text">';
    echo '<h5>' . htmlspecialchars($brand['title']) . '</h5>';
    echo '<small>' . htmlspecialchars($brand['subtitle']) . '</small>';
    echo '</div>';
    echo '</div>';
}

/**
 * Check if user has permission for navigation item
 */
function hasPermission($item_url) {
    // Implement permission checking logic here
    // For now, return true for all items
    return true;
}

/**
 * Get user's role-based navigation
 */
function getRoleBasedNavigation() {
    global $navigation_menu;
    
    // Filter navigation based on user role
    $user_role = $_SESSION['admin_role'] ?? 'administrator';
    
    // For now, return all navigation items
    // You can implement role-based filtering here
    return $navigation_menu;
}

/**
 * Add custom CSS classes to navigation items
 */
function getNavItemClasses($item_url) {
    global $current_page;
    
    $classes = ['nav-link'];
    
    if ($current_page == $item_url) {
        $classes[] = 'active';
    }
    
    // Add additional classes based on item type
    if (strpos($item_url, 'add') !== false) {
        $classes[] = 'nav-link-add';
    }
    
    return implode(' ', $classes);
}

/**
 * Get navigation item tooltip
 */
function getNavItemTooltip($item_label) {
    return 'Go to ' . $item_label;
}

// Export functions for use in other files
if (!function_exists('renderSidebarNavigation')) {
    // Functions are already defined above
}

?>

<!-- 
This file provides helper functions for sidebar navigation.
The actual sidebar HTML is rendered in admin_header.php for better layout integration.

Usage examples:
- renderSidebarNavigation() - Renders complete navigation
- renderSidebarBrand() - Renders brand section
- isActivePage($page) - Checks if page is active
- getNavBadgeCount($type) - Gets badge count for navigation items
-->
