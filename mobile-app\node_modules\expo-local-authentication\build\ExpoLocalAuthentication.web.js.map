{"version": 3, "file": "ExpoLocalAuthentication.web.js", "sourceRoot": "", "sources": ["../src/ExpoLocalAuthentication.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAsB,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAEhF,eAAe;IACb,IAAI,IAAI;QACN,OAAO,yBAAyB,CAAC;IACnC,CAAC;IACD,KAAK,CAAC,gBAAgB;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,KAAK,CAAC,eAAe;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,KAAK,CAAC,qBAAqB;QACzB,OAAO,aAAa,CAAC,IAAI,CAAC;IAC5B,CAAC;IACD,KAAK,CAAC,iCAAiC;QACrC,OAAO,EAAE,CAAC;IACZ,CAAC;CACF,CAAC", "sourcesContent": ["import { AuthenticationType, SecurityLevel } from './LocalAuthentication.types';\n\nexport default {\n  get name(): string {\n    return 'ExpoLocalAuthentication';\n  },\n  async hasHardwareAsync(): Promise<boolean> {\n    return false;\n  },\n  async isEnrolledAsync(): Promise<boolean> {\n    return false;\n  },\n  async getEnrolledLevelAsync(): Promise<SecurityLevel> {\n    return SecurityLevel.NONE;\n  },\n  async supportedAuthenticationTypesAsync(): Promise<AuthenticationType[]> {\n    return [];\n  },\n};\n"]}