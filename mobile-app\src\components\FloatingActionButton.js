import React, { useRef } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  Animated,
  View,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { theme } from '../theme/theme';

export const FloatingActionButton = ({
  onPress,
  icon = 'add',
  size = 56,
  color = 'primary',
  style,
  gradient = true,
  ...props
}) => {
  const scaleValue = useRef(new Animated.Value(1)).current;
  const rotateValue = useRef(new Animated.Value(0)).current;

  const handlePressIn = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: 0.9,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
      Animated.timing(rotateValue, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
      Animated.timing(rotateValue, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const rotate = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '45deg'],
  });

  const buttonStyle = [
    styles.fab,
    {
      width: size,
      height: size,
      borderRadius: size / 2,
    },
    style,
  ];

  const animatedStyle = {
    transform: [
      { scale: scaleValue },
      { rotate: icon === 'add' ? rotate : '0deg' },
    ],
  };

  const iconSize = size * 0.4;

  return (
    <Animated.View style={[animatedStyle, styles.container]}>
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.8}
        {...props}
      >
        {gradient ? (
          <LinearGradient
            colors={theme.gradients[color] || theme.gradients.primary}
            style={buttonStyle}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons
              name={icon}
              size={iconSize}
              color="white"
            />
          </LinearGradient>
        ) : (
          <View style={[buttonStyle, { backgroundColor: theme.colors[color] || theme.colors.primary }]}>
            <Ionicons
              name={icon}
              size={iconSize}
              color="white"
            />
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: theme.spacing.xl,
    right: theme.spacing.xl,
    zIndex: 1000,
  },
  fab: {
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.floating,
  },
});

export default FloatingActionButton;
