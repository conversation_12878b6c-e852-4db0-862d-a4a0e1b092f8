/**
 * Modern Tables JavaScript Enhancement
 * Provides enhanced functionality for admin table interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize tooltips for all elements with data-bs-toggle="tooltip"
    initializeTooltips();
    
    // Initialize search functionality
    initializeTableSearch();
    
    // Initialize row animations
    initializeRowAnimations();
    
    // Initialize loading states
    initializeLoadingStates();
    
    // Initialize responsive table enhancements
    initializeResponsiveEnhancements();
});

/**
 * Initialize Bootstrap tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            delay: { show: 500, hide: 100 }
        });
    });
}

/**
 * Initialize table search functionality
 */
function initializeTableSearch() {
    const searchInputs = document.querySelectorAll('[id$="Search"]');
    
    searchInputs.forEach(function(searchInput) {
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const tableId = this.id.replace('Search', '');
                const table = document.querySelector('.table');
                
                if (table) {
                    const rows = table.querySelectorAll('tbody tr');
                    
                    rows.forEach(function(row) {
                        const text = row.textContent.toLowerCase();
                        const shouldShow = text.includes(searchTerm);
                        
                        if (shouldShow) {
                            row.style.display = '';
                            row.classList.add('fade-in-up');
                        } else {
                            row.style.display = 'none';
                            row.classList.remove('fade-in-up');
                        }
                    });
                    
                    // Update empty state
                    updateEmptyState(table, searchTerm);
                }
            });
            
            // Add search icon animation
            const searchIcon = searchInput.parentElement.querySelector('.fa-search');
            if (searchIcon) {
                searchInput.addEventListener('focus', function() {
                    searchIcon.style.transform = 'scale(1.1)';
                    searchIcon.style.color = '#0d6efd';
                });
                
                searchInput.addEventListener('blur', function() {
                    searchIcon.style.transform = 'scale(1)';
                    searchIcon.style.color = '';
                });
            }
        }
    });
}

/**
 * Update empty state when no search results
 */
function updateEmptyState(table, searchTerm) {
    const tbody = table.querySelector('tbody');
    const visibleRows = tbody.querySelectorAll('tr[style=""], tr:not([style])');
    
    // Remove existing empty state
    const existingEmptyState = tbody.querySelector('.search-empty-state');
    if (existingEmptyState) {
        existingEmptyState.remove();
    }
    
    if (visibleRows.length === 0 && searchTerm) {
        const emptyRow = document.createElement('tr');
        emptyRow.className = 'search-empty-state';
        emptyRow.innerHTML = `
            <td colspan="100%" class="text-center py-5">
                <div class="mb-3">
                    <i class="fas fa-search fa-2x text-muted"></i>
                </div>
                <h5 class="text-muted">No results found</h5>
                <p class="text-muted mb-0">Try adjusting your search terms</p>
            </td>
        `;
        tbody.appendChild(emptyRow);
    }
}

/**
 * Initialize row animations
 */
function initializeRowAnimations() {
    const tableRows = document.querySelectorAll('.table tbody tr');
    
    // Add staggered animation on page load
    tableRows.forEach(function(row, index) {
        row.style.animationDelay = (index * 50) + 'ms';
        row.classList.add('fade-in-up');
    });
    
    // Add hover effects
    tableRows.forEach(function(row) {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

/**
 * Initialize loading states for buttons
 */
function initializeLoadingStates() {
    const actionButtons = document.querySelectorAll('.btn-group .btn, .modal .btn[type="submit"]');
    
    actionButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            if (this.type === 'submit' || this.href) {
                showLoadingState(this);
            }
        });
    });
}

/**
 * Show loading state for button
 */
function showLoadingState(button) {
    const originalContent = button.innerHTML;
    const isIcon = button.querySelector('i');
    
    if (isIcon && !button.querySelector('.spinner-border')) {
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status"></span>' + 
                          (button.textContent.trim() || 'Loading...');
        button.disabled = true;
        
        // Reset after 5 seconds as fallback
        setTimeout(function() {
            button.innerHTML = originalContent;
            button.disabled = false;
        }, 5000);
    }
}

/**
 * Initialize responsive table enhancements
 */
function initializeResponsiveEnhancements() {
    const tables = document.querySelectorAll('.table-responsive');
    
    tables.forEach(function(tableContainer) {
        const table = tableContainer.querySelector('.table');
        if (table) {
            // Add scroll indicators
            addScrollIndicators(tableContainer);
            
            // Handle horizontal scroll
            tableContainer.addEventListener('scroll', function() {
                updateScrollIndicators(this);
            });
        }
    });
}

/**
 * Add scroll indicators for responsive tables
 */
function addScrollIndicators(container) {
    const leftIndicator = document.createElement('div');
    leftIndicator.className = 'scroll-indicator scroll-indicator-left';
    leftIndicator.innerHTML = '<i class="fas fa-chevron-left"></i>';
    
    const rightIndicator = document.createElement('div');
    rightIndicator.className = 'scroll-indicator scroll-indicator-right';
    rightIndicator.innerHTML = '<i class="fas fa-chevron-right"></i>';
    
    container.style.position = 'relative';
    container.appendChild(leftIndicator);
    container.appendChild(rightIndicator);
    
    // Initial state
    updateScrollIndicators(container);
}

/**
 * Update scroll indicators based on scroll position
 */
function updateScrollIndicators(container) {
    const leftIndicator = container.querySelector('.scroll-indicator-left');
    const rightIndicator = container.querySelector('.scroll-indicator-right');
    
    if (leftIndicator && rightIndicator) {
        const scrollLeft = container.scrollLeft;
        const scrollWidth = container.scrollWidth;
        const clientWidth = container.clientWidth;
        
        // Show/hide left indicator
        leftIndicator.style.opacity = scrollLeft > 10 ? '1' : '0';
        
        // Show/hide right indicator
        rightIndicator.style.opacity = scrollLeft < (scrollWidth - clientWidth - 10) ? '1' : '0';
    }
}

/**
 * Utility function to format numbers
 */
function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

/**
 * Utility function to format dates
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

/**
 * Utility function to format file sizes
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Enhanced confirmation dialog
 */
function showConfirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * Show success message
 */
function showSuccessMessage(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    // Auto remove after 5 seconds
    setTimeout(function() {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

/**
 * Show error message
 */
function showErrorMessage(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    // Auto remove after 7 seconds
    setTimeout(function() {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 7000);
}
