<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $userId = $_SESSION['user_id'];
    $stats = [];
    
    // Get total projects count
    $stmt = $conn->prepare("SELECT COUNT(*) as total_projects FROM projects WHERE status != 'deleted'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['total_projects'] = (int)$result['total_projects'];
    
    // Get active projects count
    $stmt = $conn->prepare("SELECT COUNT(*) as active_projects FROM projects WHERE status = 'active'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['active_projects'] = (int)$result['active_projects'];
    
    // Get completed projects count
    $stmt = $conn->prepare("SELECT COUNT(*) as completed_projects FROM projects WHERE status = 'completed'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['completed_projects'] = (int)$result['completed_projects'];
    
    // Get total messages count
    $stmt = $conn->prepare("SELECT COUNT(*) as total_messages FROM messages");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['total_messages'] = (int)$result['total_messages'];
    
    // Get unread messages count
    $stmt = $conn->prepare("SELECT COUNT(*) as unread_messages FROM messages WHERE status = 'unread'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['unread_messages'] = (int)$result['unread_messages'];
    
    // Get new messages (last 24 hours)
    $stmt = $conn->prepare("SELECT COUNT(*) as new_messages FROM messages WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['new_messages'] = (int)$result['new_messages'];
    
    // Get recent projects (last 30 days)
    $stmt = $conn->prepare("SELECT COUNT(*) as recent_projects FROM projects WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['recent_projects'] = (int)$result['recent_projects'];
    
    // Get user's last activity
    $stmt = $conn->prepare("SELECT last_login FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['last_activity'] = $result['last_login'];
    
    // Get system overview
    $stmt = $conn->prepare("SELECT COUNT(*) as total_users FROM users WHERE status = 'active'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['total_users'] = (int)$result['total_users'];
    
    // Get services count
    $stmt = $conn->prepare("SELECT COUNT(*) as total_services FROM services WHERE status = 'active'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['total_services'] = (int)$result['total_services'];
    
    // Calculate growth metrics (compared to last month)
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as last_month_projects 
        FROM projects 
        WHERE created_at >= DATE_SUB(DATE_SUB(NOW(), INTERVAL 30 DAY), INTERVAL 30 DAY) 
        AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $lastMonthProjects = (int)$result['last_month_projects'];
    
    if ($lastMonthProjects > 0) {
        $stats['projects_growth'] = round((($stats['recent_projects'] - $lastMonthProjects) / $lastMonthProjects) * 100, 1);
    } else {
        $stats['projects_growth'] = $stats['recent_projects'] > 0 ? 100 : 0;
    }
    
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as last_month_messages 
        FROM messages 
        WHERE created_at >= DATE_SUB(DATE_SUB(NOW(), INTERVAL 30 DAY), INTERVAL 30 DAY) 
        AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $lastMonthMessages = (int)$result['last_month_messages'];
    
    if ($lastMonthMessages > 0) {
        $stats['messages_growth'] = round((($stats['new_messages'] - $lastMonthMessages) / $lastMonthMessages) * 100, 1);
    } else {
        $stats['messages_growth'] = $stats['new_messages'] > 0 ? 100 : 0;
    }
    
    // Add additional useful stats
    $stats['stats_breakdown'] = [
        'projects' => [
            'total' => $stats['total_projects'],
            'active' => $stats['active_projects'],
            'completed' => $stats['completed_projects'],
            'recent' => $stats['recent_projects']
        ],
        'messages' => [
            'total' => $stats['total_messages'],
            'unread' => $stats['unread_messages'],
            'new_today' => $stats['new_messages']
        ],
        'system' => [
            'users' => $stats['total_users'],
            'services' => $stats['total_services']
        ]
    ];

    // Add performance metrics
    $stats['performance'] = [
        'response_time' => round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2) . 'ms',
        'memory_usage' => round(memory_get_usage() / 1024 / 1024, 2) . 'MB'
    ];

    // Add timestamp and success flag
    $stats['timestamp'] = date('Y-m-d H:i:s');
    $stats['success'] = true;

    // Add cache headers for better performance
    header('Cache-Control: private, max-age=60'); // Cache for 1 minute
    header('ETag: "' . md5(json_encode($stats)) . '"');

    echo json_encode($stats, JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database error',
        'message' => 'Failed to fetch user statistics',
        'success' => false
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error',
        'message' => 'An unexpected error occurred',
        'success' => false
    ]);
}
?>
