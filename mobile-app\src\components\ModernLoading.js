import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { theme } from '../theme/theme';

const { width: screenWidth } = Dimensions.get('window');

export const ShimmerPlaceholder = ({
  width = '100%',
  height = 20,
  borderRadius = theme.borderRadius.sm,
  style,
  duration = 1500,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const startAnimation = () => {
      animatedValue.setValue(0);
      Animated.timing(animatedValue, {
        toValue: 1,
        duration,
        useNativeDriver: true,
      }).start(() => startAnimation());
    };

    startAnimation();
  }, [animatedValue, duration]);

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-screenWidth, screenWidth],
  });

  return (
    <View
      style={[
        styles.shimmerContainer,
        {
          width,
          height,
          borderRadius,
        },
        style,
      ]}
    >
      <Animated.View
        style={[
          styles.shimmerOverlay,
          {
            transform: [{ translateX }],
          },
        ]}
      >
        <LinearGradient
          colors={theme.gradients.shimmer}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.shimmerGradient}
        />
      </Animated.View>
    </View>
  );
};

export const CardSkeleton = ({ style }) => (
  <View style={[styles.cardSkeleton, style]}>
    <View style={styles.cardHeader}>
      <ShimmerPlaceholder width={120} height={20} />
      <ShimmerPlaceholder width={60} height={16} />
    </View>
    <View style={styles.cardContent}>
      <ShimmerPlaceholder width="100%" height={16} style={{ marginBottom: 8 }} />
      <ShimmerPlaceholder width="80%" height={16} style={{ marginBottom: 8 }} />
      <ShimmerPlaceholder width="60%" height={16} />
    </View>
  </View>
);

export const ProjectCardSkeleton = ({ style }) => (
  <View style={[styles.projectCardSkeleton, style]}>
    <View style={styles.projectHeader}>
      <ShimmerPlaceholder width={150} height={24} />
      <ShimmerPlaceholder width={80} height={20} borderRadius={theme.borderRadius.full} />
    </View>
    <View style={styles.projectInfo}>
      <View style={styles.projectInfoRow}>
        <ShimmerPlaceholder width={16} height={16} borderRadius={theme.borderRadius.full} />
        <ShimmerPlaceholder width={100} height={14} style={{ marginLeft: 8 }} />
      </View>
      <View style={styles.projectInfoRow}>
        <ShimmerPlaceholder width={16} height={16} borderRadius={theme.borderRadius.full} />
        <ShimmerPlaceholder width={80} height={14} style={{ marginLeft: 8 }} />
      </View>
    </View>
    <ShimmerPlaceholder width="100%" height={40} style={{ marginTop: 12 }} />
  </View>
);

export const ListSkeleton = ({ count = 3, itemHeight = 80, style }) => (
  <View style={[styles.listSkeleton, style]}>
    {Array.from({ length: count }).map((_, index) => (
      <View key={index} style={[styles.listItem, { height: itemHeight }]}>
        <ShimmerPlaceholder 
          width={40} 
          height={40} 
          borderRadius={theme.borderRadius.full} 
        />
        <View style={styles.listItemContent}>
          <ShimmerPlaceholder width="70%" height={16} style={{ marginBottom: 8 }} />
          <ShimmerPlaceholder width="50%" height={14} />
        </View>
      </View>
    ))}
  </View>
);

export const StatCardSkeleton = ({ style }) => (
  <View style={[styles.statCardSkeleton, style]}>
    <ShimmerPlaceholder 
      width={32} 
      height={32} 
      borderRadius={theme.borderRadius.full}
      style={{ marginBottom: 12 }}
    />
    <ShimmerPlaceholder width={40} height={24} style={{ marginBottom: 8 }} />
    <ShimmerPlaceholder width={60} height={14} />
  </View>
);

export const HeaderSkeleton = ({ style }) => (
  <View style={[styles.headerSkeleton, style]}>
    <View style={styles.headerContent}>
      <View>
        <ShimmerPlaceholder width={120} height={18} style={{ marginBottom: 4 }} />
        <ShimmerPlaceholder width={80} height={16} />
      </View>
      <ShimmerPlaceholder width={80} height={36} borderRadius={theme.borderRadius.md} />
    </View>
  </View>
);

export const SearchBarSkeleton = ({ style }) => (
  <View style={[styles.searchBarSkeleton, style]}>
    <ShimmerPlaceholder 
      width="100%" 
      height={50} 
      borderRadius={theme.borderRadius.lg}
    />
  </View>
);

const styles = StyleSheet.create({
  shimmerContainer: {
    backgroundColor: theme.colors.surfaceVariant,
    overflow: 'hidden',
  },
  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: screenWidth,
  },
  shimmerGradient: {
    flex: 1,
  },
  cardSkeleton: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.small,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  cardContent: {
    marginTop: theme.spacing.sm,
  },
  projectCardSkeleton: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    marginHorizontal: theme.spacing.xs,
    ...theme.shadows.medium,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  projectInfo: {
    marginBottom: theme.spacing.md,
  },
  projectInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  listSkeleton: {
    padding: theme.spacing.md,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.outline,
  },
  listItemContent: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  statCardSkeleton: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
    flex: 1,
    marginHorizontal: theme.spacing.xs,
    ...theme.shadows.medium,
  },
  headerSkeleton: {
    backgroundColor: theme.colors.surface,
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  searchBarSkeleton: {
    backgroundColor: theme.colors.surface,
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.sm,
  },
});

export default ShimmerPlaceholder;
