{"version": 3, "file": "NativeLinearGradient.types.js", "sourceRoot": "", "sources": ["../src/NativeLinearGradient.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { PropsWithChildren } from 'react';\nimport { ViewProps } from 'react-native';\n\nexport type NativeLinearGradientProps = ViewProps &\n  PropsWithChildren<{\n    colors: number[];\n    locations?: number[] | null;\n    startPoint?: NativeLinearGradientPoint | null;\n    endPoint?: NativeLinearGradientPoint | null;\n  }>;\n\nexport type getLinearGradientBackgroundImage = (\n  colors: number[],\n  width?: number,\n  height?: number,\n  locations?: number[] | null,\n  startPoint?: NativeLinearGradientPoint | null,\n  endPoint?: NativeLinearGradientPoint | null\n) => string;\n\nexport type NativeLinearGradientPoint = [number, number];\n"]}