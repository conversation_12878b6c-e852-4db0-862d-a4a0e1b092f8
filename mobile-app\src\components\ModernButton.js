import React, { useRef } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  Animated,
  View,
  Text,
} from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { theme } from '../theme/theme';

export const ModernButton = ({
  title,
  onPress,
  variant = 'filled', // filled, outlined, text, tonal, elevated
  size = 'medium', // small, medium, large
  color = 'primary',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left', // left, right
  style,
  textStyle,
  fullWidth = false,
  borderRadius = theme.borderRadius.md,
  pressScale = 0.96,
  hapticFeedback = true,
  ...props
}) => {
  const scaleValue = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: pressScale,
      useNativeDriver: true,
      ...theme.animations.spring.gentle,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
      ...theme.animations.spring.gentle,
    }).start();
  };

  const getButtonColors = () => {
    const colorKey = theme.colors[color] || color;
    const containerColor = `${color}Container`;
    const onColor = `on${color.charAt(0).toUpperCase() + color.slice(1)}`;
    const onContainerColor = `on${color.charAt(0).toUpperCase() + color.slice(1)}Container`;
    
    switch (variant) {
      case 'filled':
        return {
          backgroundColor: disabled ? theme.colors.disabled : colorKey,
          textColor: disabled ? theme.colors.onSurface : theme.colors[onColor] || 'white',
          borderColor: 'transparent',
          elevation: disabled ? 'none' : 'small',
        };
      case 'tonal':
        return {
          backgroundColor: disabled ? theme.colors.disabled : theme.colors[containerColor] || theme.colors.surfaceVariant,
          textColor: disabled ? theme.colors.onSurface : theme.colors[onContainerColor] || colorKey,
          borderColor: 'transparent',
          elevation: 'none',
        };
      case 'outlined':
        return {
          backgroundColor: 'transparent',
          textColor: disabled ? theme.colors.disabled : colorKey,
          borderColor: disabled ? theme.colors.disabled : colorKey,
          elevation: 'none',
        };
      case 'text':
        return {
          backgroundColor: 'transparent',
          textColor: disabled ? theme.colors.disabled : colorKey,
          borderColor: 'transparent',
          elevation: 'none',
        };
      case 'elevated':
        return {
          backgroundColor: disabled ? theme.colors.disabled : theme.colors.surface,
          textColor: disabled ? theme.colors.onSurface : colorKey,
          borderColor: 'transparent',
          elevation: disabled ? 'none' : 'small',
        };
      default:
        return {
          backgroundColor: disabled ? theme.colors.disabled : colorKey,
          textColor: disabled ? theme.colors.onSurface : 'white',
          borderColor: 'transparent',
          elevation: 'none',
        };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: theme.spacing.sm,
          paddingHorizontal: theme.spacing.md,
          fontSize: 14,
          iconSize: 16,
          minHeight: 32,
        };
      case 'large':
        return {
          paddingVertical: theme.spacing.lg,
          paddingHorizontal: theme.spacing.xl,
          fontSize: 18,
          iconSize: 24,
          minHeight: 56,
        };
      default: // medium
        return {
          paddingVertical: theme.spacing.md,
          paddingHorizontal: theme.spacing.lg,
          fontSize: 16,
          iconSize: 20,
          minHeight: 44,
        };
    }
  };

  const colors = getButtonColors();
  const sizeStyles = getSizeStyles();

  const buttonStyle = [
    styles.button,
    {
      backgroundColor: colors.backgroundColor,
      borderColor: colors.borderColor,
      borderWidth: variant === 'outlined' ? 1 : 0,
      paddingVertical: sizeStyles.paddingVertical,
      paddingHorizontal: sizeStyles.paddingHorizontal,
      borderRadius,
      width: fullWidth ? '100%' : 'auto',
      opacity: disabled ? 0.6 : 1,
      minHeight: sizeStyles.minHeight,
      ...(colors.elevation !== 'none' ? theme.shadows[colors.elevation] : {}),
    },
    style,
  ];

  const textStyles = [
    styles.buttonText,
    {
      color: colors.textColor,
      fontSize: sizeStyles.fontSize,
      fontWeight: '600',
      ...theme.typography.labelLarge,
    },
    textStyle,
  ];

  const renderContent = () => (
    <View style={styles.contentContainer}>
      {loading ? (
        <ActivityIndicator 
          size="small" 
          color={colors.textColor} 
          style={styles.loader}
        />
      ) : (
        <>
          {icon && iconPosition === 'left' && (
            <Ionicons
              name={icon}
              size={sizeStyles.iconSize}
              color={colors.textColor}
              style={styles.iconLeft}
            />
          )}
          {title && <Text style={textStyles}>{title}</Text>}
          {icon && iconPosition === 'right' && (
            <Ionicons
              name={icon}
              size={sizeStyles.iconSize}
              color={colors.textColor}
              style={styles.iconRight}
            />
          )}
        </>
      )}
    </View>
  );

  return (
    <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        activeOpacity={0.8}
        {...props}
      >
        <View style={buttonStyle}>
          {renderContent()}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Specialized button variants
export const FAB = ({ icon, onPress, size = 'medium', style, ...props }) => {
  const fabSize = size === 'small' ? 40 : size === 'large' ? 64 : 56;
  
  return (
    <ModernButton
      icon={icon}
      onPress={onPress}
      variant="filled"
      style={[
        styles.fab,
        {
          width: fabSize,
          height: fabSize,
          borderRadius: fabSize / 2,
        },
        style,
      ]}
      {...props}
    />
  );
};

export const IconButton = ({ icon, onPress, size = 'medium', style, ...props }) => {
  const buttonSize = size === 'small' ? 32 : size === 'large' ? 48 : 40;
  
  return (
    <ModernButton
      icon={icon}
      onPress={onPress}
      variant="text"
      style={[
        styles.iconButton,
        {
          width: buttonSize,
          height: buttonSize,
          borderRadius: buttonSize / 2,
        },
        style,
      ]}
      {...props}
    />
  );
};

const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    textAlign: 'center',
  },
  iconLeft: {
    marginRight: theme.spacing.sm,
  },
  iconRight: {
    marginLeft: theme.spacing.sm,
  },
  loader: {
    marginRight: theme.spacing.sm,
  },
  fab: {
    position: 'absolute',
    bottom: theme.spacing.lg,
    right: theme.spacing.lg,
    ...theme.shadows.floating,
  },
  iconButton: {
    padding: 0,
  },
});

export default ModernButton;
