import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { ActivityIndicator, Text } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { theme } from '../theme/theme';

const { width } = Dimensions.get('window');

export const SkeletonLoader = ({ 
  width: skeletonWidth = '100%', 
  height = 20, 
  borderRadius = theme.borderRadius.md,
  style 
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        styles.skeleton,
        {
          width: skeletonWidth,
          height,
          borderRadius,
          opacity,
        },
        style,
      ]}
    />
  );
};

export const CardSkeleton = () => (
  <View style={styles.cardSkeleton}>
    <View style={styles.cardSkeletonHeader}>
      <SkeletonLoader width="60%" height={24} />
      <SkeletonLoader width={60} height={24} borderRadius={theme.borderRadius.full} />
    </View>
    <SkeletonLoader width="40%" height={16} style={{ marginBottom: 8 }} />
    <SkeletonLoader width="100%" height={16} style={{ marginBottom: 4 }} />
    <SkeletonLoader width="80%" height={16} style={{ marginBottom: 8 }} />
    <SkeletonLoader width="30%" height={14} />
  </View>
);

export const ListSkeleton = ({ count = 5 }) => (
  <View style={styles.listSkeleton}>
    {Array.from({ length: count }).map((_, index) => (
      <CardSkeleton key={index} />
    ))}
  </View>
);

export const EnhancedLoader = ({
  loading = true,
  text = 'Loading...',
  size = 'large',
  color = theme.colors.primary,
  style,
  showText = true,
  gradient = false,
}) => {
  const spinValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (loading) {
      const animation = Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        })
      );
      animation.start();
      return () => animation.stop();
    }
  }, [loading, spinValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  if (!loading) return null;

  return (
    <View style={[styles.loaderContainer, style]}>
      {gradient ? (
        <Animated.View style={{ transform: [{ rotate: spin }] }}>
          <LinearGradient
            colors={theme.gradients.primary}
            style={styles.gradientLoader}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </Animated.View>
      ) : (
        <ActivityIndicator size={size} color={color} />
      )}
      {showText && (
        <Text style={[styles.loaderText, { color }]}>
          {text}
        </Text>
      )}
    </View>
  );
};

export const PulseLoader = ({ size = 40, color = theme.colors.primary }) => {
  const pulseValue = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseValue, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseValue, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, [pulseValue]);

  return (
    <Animated.View
      style={[
        styles.pulseLoader,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: color,
          transform: [{ scale: pulseValue }],
        },
      ]}
    />
  );
};

const styles = StyleSheet.create({
  skeleton: {
    backgroundColor: theme.colors.borderLight,
  },
  cardSkeleton: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.small,
  },
  cardSkeletonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  listSkeleton: {
    padding: theme.spacing.md,
  },
  loaderContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.lg,
  },
  loaderText: {
    marginTop: theme.spacing.sm,
    fontSize: 16,
    fontWeight: '500',
  },
  gradientLoader: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  pulseLoader: {
    opacity: 0.6,
  },
});

export default EnhancedLoader;
