import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { theme } from '../theme/theme';

export const ModernChip = ({
  label,
  selected = false,
  onPress,
  onDelete,
  icon,
  avatar,
  variant = 'assist', // assist, filter, input, suggestion
  size = 'medium', // small, medium, large
  disabled = false,
  style,
  textStyle,
  ...props
}) => {
  const scaleValue = new Animated.Value(1);

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.96,
      useNativeDriver: true,
      ...theme.animations.spring.gentle,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
      ...theme.animations.spring.gentle,
    }).start();
  };

  const getChipColors = () => {
    switch (variant) {
      case 'filter':
        return selected
          ? {
              backgroundColor: theme.colors.secondaryContainer,
              textColor: theme.colors.onSecondaryContainer,
              borderColor: theme.colors.secondary,
            }
          : {
              backgroundColor: 'transparent',
              textColor: theme.colors.onSurfaceVariant,
              borderColor: theme.colors.outline,
            };
      case 'input':
        return {
          backgroundColor: theme.colors.surfaceVariant,
          textColor: theme.colors.onSurfaceVariant,
          borderColor: 'transparent',
        };
      case 'suggestion':
        return {
          backgroundColor: theme.colors.surface,
          textColor: theme.colors.onSurface,
          borderColor: theme.colors.outline,
        };
      default: // assist
        return selected
          ? {
              backgroundColor: theme.colors.primaryContainer,
              textColor: theme.colors.onPrimaryContainer,
              borderColor: theme.colors.primary,
            }
          : {
              backgroundColor: theme.colors.surface,
              textColor: theme.colors.onSurface,
              borderColor: theme.colors.outline,
            };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: theme.spacing.sm,
          paddingVertical: theme.spacing.xs,
          borderRadius: theme.borderRadius.md,
          fontSize: 12,
          iconSize: 14,
          minHeight: 24,
        };
      case 'large':
        return {
          paddingHorizontal: theme.spacing.lg,
          paddingVertical: theme.spacing.sm,
          borderRadius: theme.borderRadius.lg,
          fontSize: 16,
          iconSize: 20,
          minHeight: 40,
        };
      default: // medium
        return {
          paddingHorizontal: theme.spacing.md,
          paddingVertical: theme.spacing.sm,
          borderRadius: theme.borderRadius.md,
          fontSize: 14,
          iconSize: 16,
          minHeight: 32,
        };
    }
  };

  const colors = getChipColors();
  const sizeStyles = getSizeStyles();

  const chipStyle = [
    styles.chip,
    {
      backgroundColor: colors.backgroundColor,
      borderColor: colors.borderColor,
      borderWidth: variant === 'input' ? 0 : 1,
      paddingHorizontal: sizeStyles.paddingHorizontal,
      paddingVertical: sizeStyles.paddingVertical,
      borderRadius: sizeStyles.borderRadius,
      minHeight: sizeStyles.minHeight,
      opacity: disabled ? 0.6 : 1,
    },
    selected && styles.selectedChip,
    style,
  ];

  const textStyles = [
    styles.chipText,
    {
      color: colors.textColor,
      fontSize: sizeStyles.fontSize,
      fontWeight: selected ? '600' : '500',
    },
    textStyle,
  ];

  return (
    <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
      <TouchableOpacity
        style={chipStyle}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        activeOpacity={0.8}
        {...props}
      >
        <View style={styles.chipContent}>
          {avatar && (
            <View style={[styles.avatar, { marginRight: theme.spacing.xs }]}>
              {avatar}
            </View>
          )}
          
          {icon && (
            <Ionicons
              name={icon}
              size={sizeStyles.iconSize}
              color={colors.textColor}
              style={styles.leftIcon}
            />
          )}
          
          <Text style={textStyles}>{label}</Text>
          
          {onDelete && (
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={onDelete}
              hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
            >
              <Ionicons
                name="close"
                size={sizeStyles.iconSize}
                color={colors.textColor}
              />
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Specialized chip variants
export const FilterChip = ({ selected, onToggle, ...props }) => (
  <ModernChip
    variant="filter"
    selected={selected}
    onPress={onToggle}
    {...props}
  />
);

export const StatusChip = ({ status, ...props }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'completed':
        return {
          backgroundColor: theme.colors.successContainer,
          textColor: theme.colors.onSuccessContainer,
          icon: 'checkmark-circle',
        };
      case 'ongoing':
        return {
          backgroundColor: theme.colors.warningContainer,
          textColor: theme.colors.onWarningContainer,
          icon: 'play-circle',
        };
      case 'planned':
        return {
          backgroundColor: theme.colors.infoContainer,
          textColor: theme.colors.onInfoContainer,
          icon: 'time',
        };
      default:
        return {
          backgroundColor: theme.colors.surfaceVariant,
          textColor: theme.colors.onSurfaceVariant,
          icon: 'help-circle',
        };
    }
  };

  const config = getStatusConfig();

  return (
    <View
      style={[
        styles.statusChip,
        {
          backgroundColor: config.backgroundColor,
        },
      ]}
    >
      <Ionicons
        name={config.icon}
        size={12}
        color={config.textColor}
        style={styles.statusIcon}
      />
      <Text
        style={[
          styles.statusText,
          { color: config.textColor },
        ]}
      >
        {status}
      </Text>
    </View>
  );
};

export const TagChip = ({ tag, onRemove, ...props }) => (
  <ModernChip
    label={tag}
    variant="input"
    size="small"
    onDelete={onRemove}
    {...props}
  />
);

const styles = StyleSheet.create({
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  selectedChip: {
    ...theme.shadows.xs,
  },
  chipContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chipText: {
    ...theme.typography.labelMedium,
    textAlign: 'center',
  },
  leftIcon: {
    marginRight: theme.spacing.xs,
  },
  deleteButton: {
    marginLeft: theme.spacing.xs,
    padding: 2,
  },
  avatar: {
    width: 20,
    height: 20,
    borderRadius: 10,
    overflow: 'hidden',
  },
  statusChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.full,
    minWidth: 80,
  },
  statusIcon: {
    marginRight: theme.spacing.xs,
  },
  statusText: {
    ...theme.typography.labelSmall,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
});

export default ModernChip;
