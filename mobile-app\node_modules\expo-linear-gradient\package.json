{"name": "expo-linear-gradient", "version": "12.3.0", "description": "Provides a React component that renders a gradient view.", "main": "build/LinearGradient.js", "types": "build/LinearGradient.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "gradient"], "jest": {"preset": "expo-module-scripts"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-linear-gradient"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/linear-gradient/", "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/react-native": "^11.3.0", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "fa5ecca8251986b9f197cc14074eec0ab6dfb6db"}