import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { Platform, StatusBar as RNStatusBar } from 'react-native';
import { theme } from '../theme/theme';

export const EnhancedStatusBar = ({
  style = 'light',
  backgroundColor = 'transparent',
  translucent = true,
  hidden = false,
  animated = true,
  ...props
}) => {
  // Set Android status bar color
  if (Platform.OS === 'android') {
    RNStatusBar.setBackgroundColor(backgroundColor, animated);
    RNStatusBar.setTranslucent(translucent);
  }

  return (
    <StatusBar
      style={style}
      backgroundColor={backgroundColor}
      translucent={translucent}
      hidden={hidden}
      animated={animated}
      {...props}
    />
  );
};

export default EnhancedStatusBar;
