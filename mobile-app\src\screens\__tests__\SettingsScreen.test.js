import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import SettingsScreen from '../SettingsScreen';
import * as SecureStore from 'expo-secure-store';
import * as LocalAuthentication from 'expo-local-authentication';

// Mock dependencies
jest.mock('expo-secure-store');
jest.mock('expo-local-authentication');
jest.mock('../../services/apiService');
jest.mock('../../services/notificationService');

// Mock navigation
const mockNavigation = {
  navigate: jest.fn(),
  goBack: jest.fn(),
};

describe('SettingsScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    SecureStore.getItemAsync.mockResolvedValue(null);
    LocalAuthentication.hasHardwareAsync.mockResolvedValue(true);
    LocalAuthentication.isEnrolledAsync.mockResolvedValue(true);
  });

  it('renders correctly', async () => {
    const { getByText, getByPlaceholderText } = render(
      <SettingsScreen navigation={mockNavigation} />
    );

    await waitFor(() => {
      expect(getByText('Settings')).toBeTruthy();
      expect(getByText('Customize your app experience')).toBeTruthy();
      expect(getByPlaceholderText('Search settings...')).toBeTruthy();
    });
  });

  it('displays all main sections', async () => {
    const { getByText } = render(
      <SettingsScreen navigation={mockNavigation} />
    );

    await waitFor(() => {
      expect(getByText('Notifications')).toBeTruthy();
      expect(getByText('App Preferences')).toBeTruthy();
      expect(getByText('Security & Privacy')).toBeTruthy();
      expect(getByText('Data & Storage')).toBeTruthy();
      expect(getByText('Developer Options')).toBeTruthy();
      expect(getByText('Actions')).toBeTruthy();
      expect(getByText('About')).toBeTruthy();
    });
  });

  it('toggles sections when headers are pressed', async () => {
    const { getByText, queryByText } = render(
      <SettingsScreen navigation={mockNavigation} />
    );

    await waitFor(() => {
      expect(getByText('Notifications')).toBeTruthy();
    });

    // Initially notifications section should be expanded
    expect(queryByText('Enable Notifications')).toBeTruthy();

    // Tap to collapse
    fireEvent.press(getByText('Notifications'));
    
    await waitFor(() => {
      expect(queryByText('Enable Notifications')).toBeFalsy();
    });
  });

  it('handles search functionality', async () => {
    const { getByPlaceholderText, getByText } = render(
      <SettingsScreen navigation={mockNavigation} />
    );

    const searchInput = getByPlaceholderText('Search settings...');
    
    fireEvent.changeText(searchInput, 'notification');
    
    await waitFor(() => {
      // Should filter settings based on search
      expect(getByText('Notifications')).toBeTruthy();
    });
  });

  it('handles biometric authentication toggle', async () => {
    LocalAuthentication.authenticateAsync.mockResolvedValue({ success: true });
    
    const { getByText } = render(
      <SettingsScreen navigation={mockNavigation} />
    );

    await waitFor(() => {
      expect(getByText('Security & Privacy')).toBeTruthy();
    });

    // Expand security section
    fireEvent.press(getByText('Security & Privacy'));

    await waitFor(() => {
      expect(getByText('Biometric Authentication')).toBeTruthy();
    });

    // Toggle biometric authentication
    fireEvent.press(getByText('Biometric Authentication'));

    await waitFor(() => {
      expect(LocalAuthentication.authenticateAsync).toHaveBeenCalled();
    });
  });

  it('saves settings to secure storage', async () => {
    const { getByText } = render(
      <SettingsScreen navigation={mockNavigation} />
    );

    await waitFor(() => {
      expect(getByText('App Preferences')).toBeTruthy();
    });

    // Expand preferences section
    fireEvent.press(getByText('App Preferences'));

    await waitFor(() => {
      expect(getByText('Auto Sync')).toBeTruthy();
    });

    // Toggle auto sync
    fireEvent.press(getByText('Auto Sync'));

    await waitFor(() => {
      expect(SecureStore.setItemAsync).toHaveBeenCalledWith(
        'appSettings',
        expect.stringContaining('"autoSync"')
      );
    });
  });

  it('handles export settings action', async () => {
    const { getByText } = render(
      <SettingsScreen navigation={mockNavigation} />
    );

    await waitFor(() => {
      expect(getByText('Actions')).toBeTruthy();
    });

    await waitFor(() => {
      expect(getByText('Export Settings')).toBeTruthy();
    });

    fireEvent.press(getByText('Export Settings'));

    // Should show export dialog (mocked Alert.alert would be called)
  });

  it('handles clear cache action', async () => {
    const { getByText } = render(
      <SettingsScreen navigation={mockNavigation} />
    );

    await waitFor(() => {
      expect(getByText('Clear Cache')).toBeTruthy();
    });

    fireEvent.press(getByText('Clear Cache'));

    // Should show confirmation dialog (mocked Alert.alert would be called)
  });
});
