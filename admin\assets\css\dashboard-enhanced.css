/* Enhanced Dashboard Styling */

/* Enhanced Stats Cards */
.stats-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border-radius: 16px !important;
    border: 1px solid rgba(0, 0, 0, 0.05);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.stats-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

.stats-content {
    position: relative;
    z-index: 2;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #212529;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stats-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.75rem;
}

.stats-trend {
    font-size: 0.75rem;
    margin-top: 0.5rem;
}

.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    margin-right: 0.5rem;
}

.trend-up {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.trend-down {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.completion-rate {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: 600;
    color: #198754;
}

/* Progress Bar Styling */
.progress-sm {
    height: 4px;
    margin-bottom: 0.25rem;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, 0.05);
}

.progress-bar {
    border-radius: 2px;
    transition: width 0.6s ease;
}

/* Quick Stats Cards */
.quick-stat-card {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 12px;
    padding: 1.25rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    height: 100%;
}

.quick-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: rgba(13, 110, 253, 0.2);
}

.quick-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.quick-stat-number {
    font-size: 1.75rem;
    font-weight: 700;
    color: #212529;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.quick-stat-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Chart Container Styling */
.card .card-body canvas {
    max-height: 300px;
}

/* Message List Styling */
.message-list {
    max-height: 400px;
    overflow-y: auto;
}

.message-item {
    transition: all 0.2s ease;
    padding: 0.5rem;
    border-radius: 8px;
    margin: -0.5rem;
    margin-bottom: 0.5rem;
}

.message-item:hover {
    background-color: rgba(255, 193, 7, 0.05);
    transform: translateX(4px);
}

.message-item:last-child {
    margin-bottom: -0.5rem;
}

/* Button Enhancements */
.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 6px;
}

/* Enhanced Card Headers */
.card-header.bg-white {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* Loading States */
.fa-spin {
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Notification Styling */
.alert.position-fixed {
    border-radius: 12px;
    border: none;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

.alert-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.9) 0%, rgba(25, 135, 84, 0.8) 100%);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.9) 0%, rgba(13, 202, 240, 0.8) 100%);
    color: white;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .stats-number {
        font-size: 2rem;
    }
    
    .quick-stat-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .quick-stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .quick-stat-number {
        font-size: 1.5rem;
    }
    
    .message-list {
        max-height: 300px;
    }
    
    .card-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem;
    }
    
    .card-header .btn {
        align-self: flex-end;
    }
}

@media (max-width: 576px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .stats-number {
        font-size: 1.75rem;
    }
    
    .quick-stat-number {
        font-size: 1.25rem;
    }
    
    .page-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }
    
    .page-header .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    .stats-card {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .stats-number {
        color: #f7fafc;
    }
    
    .stats-label {
        color: #a0aec0;
    }
    
    .quick-stat-card {
        background: #2d3748;
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .quick-stat-number {
        color: #f7fafc;
    }
    
    .quick-stat-label {
        color: #a0aec0;
    }
}

/* Print Styles */
@media print {
    .stats-card,
    .quick-stat-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .btn,
    .alert {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
