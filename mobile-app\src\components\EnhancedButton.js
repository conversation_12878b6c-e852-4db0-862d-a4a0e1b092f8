import React, { useRef } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  Animated,
  View,
} from 'react-native';
import { Text, ActivityIndicator } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { theme } from '../theme/theme';

export const EnhancedButton = ({
  title,
  onPress,
  variant = 'contained', // contained, outlined, text, gradient
  size = 'medium', // small, medium, large
  color = 'primary',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left', // left, right
  style,
  textStyle,
  fullWidth = false,
  borderRadius = theme.borderRadius.lg,
  ...props
}) => {
  const scaleValue = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.96,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  const getButtonColors = () => {
    const colorKey = color === 'primary' ? 'primary' : theme.colors[color] || color;
    
    switch (variant) {
      case 'contained':
        return {
          backgroundColor: disabled ? theme.colors.disabled : colorKey,
          textColor: 'white',
          borderColor: 'transparent',
        };
      case 'outlined':
        return {
          backgroundColor: 'transparent',
          textColor: disabled ? theme.colors.disabled : colorKey,
          borderColor: disabled ? theme.colors.disabled : colorKey,
        };
      case 'text':
        return {
          backgroundColor: 'transparent',
          textColor: disabled ? theme.colors.disabled : colorKey,
          borderColor: 'transparent',
        };
      case 'gradient':
        return {
          backgroundColor: 'transparent',
          textColor: 'white',
          borderColor: 'transparent',
        };
      default:
        return {
          backgroundColor: disabled ? theme.colors.disabled : colorKey,
          textColor: 'white',
          borderColor: 'transparent',
        };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: theme.spacing.sm,
          paddingHorizontal: theme.spacing.md,
          fontSize: 14,
          iconSize: 16,
        };
      case 'large':
        return {
          paddingVertical: theme.spacing.lg,
          paddingHorizontal: theme.spacing.xl,
          fontSize: 18,
          iconSize: 24,
        };
      default: // medium
        return {
          paddingVertical: theme.spacing.md,
          paddingHorizontal: theme.spacing.lg,
          fontSize: 16,
          iconSize: 20,
        };
    }
  };

  const colors = getButtonColors();
  const sizeStyles = getSizeStyles();

  const buttonStyle = [
    styles.button,
    {
      backgroundColor: colors.backgroundColor,
      borderColor: colors.borderColor,
      borderWidth: variant === 'outlined' ? 1 : 0,
      paddingVertical: sizeStyles.paddingVertical,
      paddingHorizontal: sizeStyles.paddingHorizontal,
      borderRadius,
      width: fullWidth ? '100%' : 'auto',
      opacity: disabled ? 0.6 : 1,
    },
    style,
  ];

  const textStyles = [
    styles.buttonText,
    {
      color: colors.textColor,
      fontSize: sizeStyles.fontSize,
      fontWeight: '600',
    },
    textStyle,
  ];

  const renderContent = () => (
    <View style={styles.contentContainer}>
      {loading ? (
        <ActivityIndicator 
          size="small" 
          color={colors.textColor} 
          style={styles.loader}
        />
      ) : (
        <>
          {icon && iconPosition === 'left' && (
            <Ionicons
              name={icon}
              size={sizeStyles.iconSize}
              color={colors.textColor}
              style={styles.iconLeft}
            />
          )}
          <Text style={textStyles}>{title}</Text>
          {icon && iconPosition === 'right' && (
            <Ionicons
              name={icon}
              size={sizeStyles.iconSize}
              color={colors.textColor}
              style={styles.iconRight}
            />
          )}
        </>
      )}
    </View>
  );

  return (
    <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        activeOpacity={0.8}
        {...props}
      >
        {variant === 'gradient' && !disabled ? (
          <LinearGradient
            colors={theme.gradients[color] || theme.gradients.primary}
            style={buttonStyle}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            {renderContent()}
          </LinearGradient>
        ) : (
          <View style={buttonStyle}>
            {renderContent()}
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    textAlign: 'center',
  },
  iconLeft: {
    marginRight: theme.spacing.sm,
  },
  iconRight: {
    marginLeft: theme.spacing.sm,
  },
  loader: {
    marginRight: theme.spacing.sm,
  },
});

export default EnhancedButton;
