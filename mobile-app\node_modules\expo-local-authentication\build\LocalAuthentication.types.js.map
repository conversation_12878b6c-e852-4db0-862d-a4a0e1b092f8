{"version": 3, "file": "LocalAuthentication.types.js", "sourceRoot": "", "sources": ["../src/LocalAuthentication.types.ts"], "names": [], "mappings": "AAIA,cAAc;AACd,MAAM,CAAN,IAAY,kBAcX;AAdD,WAAY,kBAAkB;IAC5B;;OAEG;IACH,yEAAe,CAAA;IACf;;OAEG;IACH,uFAAsB,CAAA;IACtB;;;OAGG;IACH,2DAAQ,CAAA;AACV,CAAC,EAdW,kBAAkB,KAAlB,kBAAkB,QAc7B;AAED,cAAc;AACd,MAAM,CAAN,IAAY,aAaX;AAbD,WAAY,aAAa;IACvB;;OAEG;IACH,iDAAQ,CAAA;IACR;;OAEG;IACH,qDAAU,CAAA;IACV;;OAEG;IACH,2DAAa,CAAA;AACf,CAAC,EAbW,aAAa,KAAb,aAAa,QAaxB", "sourcesContent": ["export type LocalAuthenticationResult =\n  | { success: true }\n  | { success: false; error: string; warning?: string };\n\n// @needsAudit\nexport enum AuthenticationType {\n  /**\n   * Indicates fingerprint support.\n   */\n  FINGERPRINT = 1,\n  /**\n   * Indicates facial recognition support.\n   */\n  FACIAL_RECOGNITION = 2,\n  /**\n   * Indicates iris recognition support.\n   * @platform android\n   */\n  IRIS = 3,\n}\n\n// @needsAudit\nexport enum SecurityLevel {\n  /**\n   * Indicates no enrolled authentication.\n   */\n  NONE = 0,\n  /**\n   * Indicates non-biometric authentication (e.g. PIN, Pattern).\n   */\n  SECRET = 1,\n  /**\n   * Indicates biometric authentication.\n   */\n  BIOMETRIC = 2,\n}\n\n// @needsAudit\nexport type LocalAuthenticationOptions = {\n  /**\n   * A message that is shown alongside the TouchID or FaceID prompt.\n   */\n  promptMessage?: string;\n  /**\n   * Allows to customize the default `Cancel` label shown.\n   */\n  cancelLabel?: string;\n  /**\n   * After several failed attempts the system will fallback to the device passcode. This setting\n   * allows you to disable this option and instead handle the fallback yourself. This can be\n   * preferable in certain custom authentication workflows. This behaviour maps to using the iOS\n   * [LAPolicyDeviceOwnerAuthenticationWithBiometrics](https://developer.apple.com/documentation/localauthentication/lapolicy/lapolicydeviceownerauthenticationwithbiometrics?language=objc)\n   * policy rather than the [LAPolicyDeviceOwnerAuthentication](https://developer.apple.com/documentation/localauthentication/lapolicy/lapolicydeviceownerauthentication?language=objc)\n   * policy. Defaults to `false`.\n   */\n  disableDeviceFallback?: boolean;\n  /**\n   * Sets a hint to the system for whether to require user confirmation after authentication.\n   * This may be ignored by the system if the user has disabled implicit authentication in Settings\n   * or if it does not apply to a particular biometric modality. Defaults to `true`.\n   * @platform android\n   */\n  requireConfirmation?: boolean;\n  /**\n   * Allows to customize the default `Use Passcode` label shown after several failed\n   * authentication attempts. Setting this option to an empty string disables this button from\n   * showing in the prompt.\n   * @platform ios\n   */\n  fallbackLabel?: string;\n};\n"]}