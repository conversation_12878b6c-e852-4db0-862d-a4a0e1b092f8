import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { theme } from '../theme/theme';

export const EnhancedCard = ({
  children,
  style,
  onPress,
  gradient = false,
  gradientColors = theme.gradients.card,
  shadow = 'none',
  borderRadius = theme.borderRadius.lg,
  padding = theme.spacing.md,
  margin = 0,
  animated = true,
  ...props
}) => {
  const scaleValue = new Animated.Value(1);

  const handlePressIn = () => {
    if (animated && onPress) {
      Animated.spring(scaleValue, {
        toValue: 0.98,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animated && onPress) {
      Animated.spring(scaleValue, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }).start();
    }
  };

  const cardStyle = [
    styles.card,
    {
      borderRadius,
      padding,
      margin,
      ...theme.shadows[shadow],
    },
    style,
  ];

  const animatedStyle = animated ? {
    transform: [{ scale: scaleValue }],
  } : {};

  const CardContent = () => (
    <View style={[cardStyle, !gradient && { backgroundColor: theme.colors.surface }]}>
      {children}
    </View>
  );

  if (onPress) {
    return (
      <Animated.View style={animatedStyle}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.9}
          {...props}
        >
          {gradient ? (
            <LinearGradient
              colors={gradientColors}
              style={cardStyle}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              {children}
            </LinearGradient>
          ) : (
            <CardContent />
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={animatedStyle}>
      {gradient ? (
        <LinearGradient
          colors={gradientColors}
          style={cardStyle}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {children}
        </LinearGradient>
      ) : (
        <CardContent />
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  card: {
    overflow: 'hidden',
  },
});

export default EnhancedCard;
